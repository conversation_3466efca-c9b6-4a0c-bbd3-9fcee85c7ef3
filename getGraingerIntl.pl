#!/usr/bin/perl

use strict;
use URI::Escape;

my $header_file = "/local/search/data/interactions/adobe_agi/column_headers.tsv";
my $tmpStr;
my @tmpArr;
my %headers;
my $numFields;
my $i = 0;
my $product;
my $keyword;
my $productKeyword;
my $date;
my $customer;
my $referrer;
my $url;
my $referral;
my $pageName;
my $productList;
my %carts = ();
my $CART_ADD = 12;
my $CART_PURCHASE = 1;
my $file;
my $sapID;
my $sapAcc;
my $demandBase;
my $industry;
my $subIndustry;
my $audience;
my $audienceSegment;
my $countryCode = "agi";
my $language;
my @acceptedLanguages = ('en', 'fr', 'es');

sub checkCountry() {
   if(index($file, "adobe_mx") != -1) {
      $header_file = "/local/search/data/interactions/adobe_mx/column_headers.tsv";
      $countryCode = "mx";
   }
}

sub loadHeaders() {
   open(HEADERS, "<", "$header_file") or die "cannot open < $header_file: $!";
   $tmpStr = <HEADERS>;
   close(HEADERS);

   chomp($tmpStr);
   @tmpArr = split(/\t/,$tmpStr,-1);
   foreach my $k (@tmpArr) {
      $headers{$k} = $i++;
   }
   $numFields = scalar(keys %headers);
}

sub loadCarts() {
   my $product;
   my $action;
   my $actions;
   my $data;
   open(CART, "<", "$file") or die "cannot open < $file: $!";
   while(<CART>) {
      chomp;
      @tmpArr = split(/\t/,$_,-1);
      if(scalar(@tmpArr) != $numFields) {
         # Invalid data row from Adobe
         next;
      }
      $actions = $tmpArr[$headers{'event_list'}];
      $customer = lc($tmpArr[$headers{'post_visid_high'}])."_".lc($tmpArr[$headers{'post_visid_low'}]);
      $data = lc($tmpArr[$headers{'product_list'}]);
      $product = '';
      $action = '';

      #;3C591;;;218=3|219=80.4|220=80.4;eVar39=ST|eVar40=PDPD
      if($actions =~ m/^$CART_ADD,|,$CART_ADD$|,$CART_ADD,|^$CART_ADD$/) {
         if($data =~ m/^;(.*?);.*/) {
             $action = $CART_ADD;
             $product = $1;
         }
      }

      #;1GEK3;2;93.6,;2LJJ2;2;43.4,;2LJG9;2;54.3
      if($actions =~ m/^$CART_PURCHASE,|,$CART_PURCHASE$|,$CART_PURCHASE,|^$CART_PURCHASE$/) {
         if($data =~ m/^;(.*?);.*/) {
             $action = $CART_PURCHASE;
             $product = $1;
         }
      }

      if($product ne '' && $action ne '' && $customer ne '') {
         $carts{$customer}{$product} = $action;
      }
   }
   close(CART);
}

sub cleanKeyword($) {
   my $url = shift;

         # AcklandsGrainger search result page.  Order here is important.  For example, there could be a redirect to an L3 with a keyword refinement.  The redirect needs to take precedence over the text.
         if($url =~ /redirectTerm=([^&#]+)/) {
            $keyword = $1;
         }    
         elsif($url =~ /text=([^&#]+)/) {
            $keyword = $1;
         }  
         else {
            $keyword = '';
         }  
   
         $keyword = uri_unescape($keyword);
         $keyword =~ s/\|.*//;
         if($keyword =~ /img|http|https|=/) {
            $keyword = '';
         }
         if($keyword =~ /[^[:print:]]/) {
            $keyword = '';
         }
         $keyword =~ s/&quot;/"/g;
         if($keyword =~ /^"(.*)"$/) {
            $keyword = $1;
         }
         $keyword =~ s/\|/ /g;
         $keyword =~ s/,/ /g;
         $keyword =~ s/\\/ /g;
         $keyword =~ s/\+/ /g;
         $keyword =~ s/[ ]+/ /g;
         $keyword =~ s/^[ ]//g;
         $keyword =~ s/[ ]$//g;
    return $keyword;
}

sub setValidLang($) {
   my $lang = shift;
   my $defaultLang = undef;
   if (grep $_ eq $lang, @acceptedLanguages) {
      return $language;
   } else {
      # set default lang to English/Spanish, when its not set in Adobe.
      if ($countryCode eq "mx") {
            $defaultLang = "es";
         } else {
            $defaultLang = "en";
         }
   }
   return $defaultLang;
}

sub mineEvents() {

   open(FILE, "<", "$file") or die "cannot open < $file: $!";
   	my $childSKURef;
    my $webparent;
   while(<FILE>) {
      chomp;
      @tmpArr = split(/\t/,$_,-1);
      if(scalar(@tmpArr) != $numFields) {
         # Invalid data row from Adobe
         next;
      }
      $url = lc($tmpArr[$headers{'page_url'}]);
      $referrer = lc($tmpArr[$headers{'post_referrer'}]);
      $pageName = lc($tmpArr[$headers{'pagename'}]);
      $productList = lc($tmpArr[$headers{'product_list'}]);
      $language = lc($tmpArr[$headers{'evar62'}]);
      $keyword = '';
      $childSKURef = '';
      $webparent = '';
      # Check referrer in when going from results to IDP
      $referrer =~ s/#.*//; # Avoid false matches in client-side url
      if ($countryCode eq "mx") {
         if(($referrer =~ /^http[s]?:\/\/www\.grainger\.com\.mx\/search/ || $referrer =~ /^http[s]?:\/\/www\.grainger\.com\.mx\/category/) && ($referrer =~ /text=([^&#]+)/ || $referrer =~ /redirectTerm=([^&#]+)/)) {
            $keyword = cleanKeyword($referrer);
         }
      } else {
         if(($referrer =~ /^http[s]?:\/\/www\.acklandsgrainger\.com\/.{2}\/search/ || $referrer =~ /^http[s]?:\/\/www\.acklandsgrainger\.com\/.{2}\/category/ || $referrer =~ /^http[s]?:\/\/www\.grainger\.ca\/.{2}\/search/ || $referrer =~ /^http[s]?:\/\/www\.grainger\.ca\/.{2}\/category/) && ($referrer =~ /text=([^&#]+)/ || $referrer =~ /redirectTerm=([^&#]+)/)) {
            $keyword = cleanKeyword($referrer);
         }
      }
      
      $url =~ s/#.*//; # Avoid false matches in client-side url
      if(($url =~ /\/product\// || $url =~ /\/produit\// || $url =~ /\/producto\//) && $url !~ /\/compare/ && $keyword ne "::empty::" && $keyword ne "-" && $keyword ne "" && $keyword !~ /acklandsgrainger/ && $keyword !~ /grainger/) {
         $product = $url;
         $product =~ s/#.*//;

         # Skip sku search events that happen immediately following search results.  The product page is not related to the keyword search.
         $productKeyword = $keyword;
         if($product =~ /searchBar=([^&#]+)/) { # https://www.acklandsgrainger.com/en/product/COMPACT-MAGNETIC-DRILL%2C600LBS%2C13A/p/MTL4272-21?searchBar=true
            $productKeyword = $1;
            chomp($productKeyword);
         }
         if($keyword ne $productKeyword) {
            next;
         }
         
         $product =~ s/\?.*$//;
         if ($countryCode eq "mx") {
            $product =~ s/^.*\/producto\///;
         }  else {
               if ($language eq "en") {
                  $product =~ s/^.*\/product\///;
               } else {
                  $product =~ s/^.*\/produit\///;
               }
         }
         $product =~ s/^[^\/]*\/p\///; # Throw this event away if we get here and this matches

         if ($countryCode eq "mx") {
            $referral = "grainger";
         } else {
            $referral = "acklandsgrainger";
         }
         $language = setValidLang($language);
         $date = lc($tmpArr[$headers{'date_time'}]);
         $customer = lc($tmpArr[$headers{'post_visid_high'}])."_".lc($tmpArr[$headers{'post_visid_low'}]);
   
         $sapID = lc($tmpArr[$headers{'post_evar19'}]);
	 $sapAcc = lc($tmpArr[$headers{'post_evar20'}]);
	 $demandBase = lc($tmpArr[$headers{'post_evar11'}]);
	 $industry = '';
	 $subIndustry = '';
	 $audience = '';
	 $audienceSegment = '';

	 # 14445400:Balfour Beatty Capital Group Inc:COMMERCIAL SERVICES:FINANCIAL:Enterprise:Over $5B:Enterprise Business:Financial Services
	 if(defined $demandBase and length $demandBase) {
		my @demandBaseInfo = split(':', $demandBase, -1);
                # -1 for empty values 
		if (defined $demandBaseInfo[0] and length $demandBaseInfo[0] and $demandBaseInfo[0] ne "[n/a]" ) {
			$industry = $demandBaseInfo[2];
         		$subIndustry = $demandBaseInfo[3];
         		$audience = $demandBaseInfo[6];
         		$audienceSegment = $demandBaseInfo[7];
		}			
   }
         $date =~ s/^"//; $date =~ s/"$//;
         $sapAcc =~ s/^"//; $sapAcc =~ s/"$//;
         $industry =~ s/^"//; $industry =~ s/"$//;
         $subIndustry =~ s/^"//; $subIndustry =~ s/"$//;


         # If valid product and keyword is not a simple product lookup
         if ($product ne "" && $keyword ne $product) {
         	$childSKURef = $product;
            if(defined($carts{$customer}{$product}) && $carts{$customer}{$product} eq $CART_PURCHASE) {
               # PURCHASE event
               print "$keyword,$product,$date,$customer,0,0,1,$countryCode,$sapID,$sapAcc,$industry,$subIndustry,$audience,$audienceSegment,$childSKURef,$language\n";
            }
            elsif(defined($carts{$customer}{$product}) && $carts{$customer}{$product} eq $CART_ADD) {
               # ATC event
               print "$keyword,$product,$date,$customer,0,1,0,$countryCode,$sapID,$sapAcc,$industry,$subIndustry,$audience,$audienceSegment,$childSKURef,$language\n";
            }
            else {
               # VIEW event
               print "$keyword,$product,$date,$customer,1,0,0,$countryCode,$sapID,$sapAcc,$industry,$subIndustry,$audience,$audienceSegment,$childSKURef,$language\n";
            }
         }
      }
   }
   close(FILE);
}
   
$file = $ARGV[0];
checkCountry();
loadHeaders();
loadCarts();
mineEvents();
exit(0);
   
