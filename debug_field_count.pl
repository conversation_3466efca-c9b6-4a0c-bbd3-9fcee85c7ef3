#!/usr/bin/perl

# Original data from user
my $original = '20236=183,211,243,20208,100,101,102,103,106,108,109,113,114,127,133,134,147,148,154,155,156,157,158,159,160,161,162,163,164,166,167,170,178,188,191,192,193,194,10001,10002,10003,10004,10011,10015,10018,10020,10027,10031,10038,10053,10064,10065,10068,10074,10075,10076,10103,10125,10141,10149     4321280761088827931     1744885501547481282     None    https://www.grainger.com/category/tools/hand-tools/pliers/retaining-ring-pliers-sets?filters=webParentSkuKey&searchQuery=snap+ring+pliers&webParentSkuKey=WP13493127%7CWP13493128&sst=4&tv_optin=true   https://www.grainger.com/category/tools/hand-tools/files/material-removing-american-pattern-files?categoryIndex=1       grainger/category/tools/hand tools/pliers/retaining-ring plier  2025-02-01 00:00:00                             ::empty::       80      google.com';

print "Original data field analysis:\n";

# Convert multiple spaces to single tabs
$original =~ s/\s+/\t/g;

my @fields = split(/\t/, $original);
print "Number of fields after space-to-tab conversion: " . scalar(@fields) . "\n";

for my $i (0..$#fields) {
    print "[$i] '$fields[$i]'\n";
}

print "\nExpected 14 fields:\n";
my @expected = qw(event_list post_visid_high post_visid_low product_list page_url post_referrer pagename date_time post_evar19 post_evar20 post_evar11 visit_keywords visit_page_num visit_ref_domain);

for my $i (0..13) {
    my $field_name = $expected[$i];
    my $actual_value = $fields[$i] || "MISSING";
    print "[$i] $field_name: '$actual_value'\n";
}
