from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler
import subprocess
import sys
import argparse
import logging
import os

logger = logging.getLogger(__name__)
logger.addHandler(RotatingFileHandler('get_clickstream.log', maxBytes=100000000, backupCount=5))
logging.basicConfig(level=logging.INFO, filename='get_clickstream.log', filemode='a', format='%(asctime)s - %(levelname)s - %(message)s')

class QueryGenerator:
    def __init__(self, template: str, country: str):
        self.template = template
        self.country = country

    def generate_query_for_dates(self, start_date: datetime):
        end_date = (start_date + timedelta(days=1)).strftime("%Y-%m-%d")
        start_date = start_date.strftime("%Y-%m-%d")

        query = self.template.replace('__start_date__', start_date). \
            replace('__end_date__', end_date)

        # write query to file
        logger.debug(f"Writing query for {start_date} to file {self.sql_file_path(start_date)}")
        with open(self.sql_file_path(start_date), 'w') as f:
            f.write(query)

    def sql_file_path(self, date: str) -> str:
        return f'out/{self.country}/clickstream-query-{date}.sql'

    def data_file_path(self, date: datetime) -> str:
        return f'out/{self.country}/clickstream-{date}.tsv'

    def cleanup(self, date: str):
        os.remove(self.sql_file_path(date))

def _generate_dates(range_start: str, range_end: str):
    start = datetime.strptime(range_start, "%Y-%m-%d")
    end = datetime.strptime(range_end, "%Y-%m-%d")

    date_array = \
        [(start + timedelta(days=x)) for x in range(0, (end-start).days)]

    # add all dates between range_start and range_end to date_range
    return date_array


class Command:
    command_str: str

    def __init__(self, tag: str, command_str: str):
        self.command_str = command_str
        self.tag = tag

    def run(self, dry_run: bool = False):

        print(' '.join(self.command_str))

        if not dry_run:
            return_code = subprocess.call(self.command_str)

            if return_code != 0:
                logger.error(f"Failed to execute {self.tag} command. Return code: {return_code}")
                sys.exit(1)

def _build_snowsql_command(query_generator: QueryGenerator, snowflake_account: str, start_date_str: str) -> Command:
    cmd_str = ['snowsql',
                '-a', snowflake_account,
                '-f', query_generator.sql_file_path(start_date_str),
                '-w', 'SEARCH_ENGINE_WH_M',
                '-o', 'output_file=' + query_generator.data_file_path(start_date_str),
                '-o', 'output_format=tsv',
                '-o', 'header=false',
                '-o', 'echo=false',
                '-o', 'insecure_mode=true',
                '-o', 'login_timeout=2000',
                '-o', 'log_level=INFO'
            ]

    return Command('snowsql', cmd_str)


def _build_s3_command(query_generator: QueryGenerator, start_date_str: str, upload_path: str, aws_profile: str) -> Command:
    cmd_str = ['aws', 's3', 'cp',
            "./" + query_generator.data_file_path(start_date_str), upload_path,
            '--profile', aws_profile]

    return Command('s3', cmd_str)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog="get_clickstream.py", description="Retrieves clickstream data from Snowflake and (optionally) uploads to s3 (hardcoded path). Produces _one file_ for _each date in the given range_. If a date range is not provided, gets clickstream data for the previous day. Needs snowsql installed, $SNOWSQL_USER and $SNOWSQL_PWD env vars set, and aws cli installed to run to completion.")
    parser.add_argument('--range-start', help='Start date of the range to backfill, in the format YYYY-MM-DD, e.g. 2021-01-01')
    parser.add_argument('--range-end', help='End date (exclusive) of the range to backfill, in the format YYYY-MM-DD, e.g. 2021-01-03')
    parser.add_argument('--dry-run', help='Run the script in debug mode (false by default)', action='store_true')
    parser.add_argument('--upload', help='Upload the data to s3 (false by default)', action='store_true')
    parser.add_argument('--upload-path', help='s3 location to upload files to', default='s3://gww-solr-preprod-s3/backups/pp0/interactions/adobe/backfill/')
    parser.add_argument('--aws-profile', help='AWS profile to use in s3 command', default='solr-qa')
    parser.add_argument('--snowflake-account', help='Snowflake account to retrieve data from (wwgraingerdev.us-east-1 OR wwgraingerqa.us-east-1 OR wwgrainger.us-east-1)', default='wwgrainger.us-east-1')
    args = parser.parse_args()

    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    today = datetime.now().strftime("%Y-%m-%d")

    range_start = args.range_start if args.range_start else yesterday
    range_end = args.range_end if args.range_end else today
    dry_run = args.dry_run
    upload = args.upload
    upload_path = args.upload_path
    aws_profile = args.aws_profile
    snowflake_account = args.snowflake_account

    start_dates = _generate_dates(range_start, range_end)

    agi_query_template = open('sql/agi_query.sql', 'r').read()
    mx_query_template = open('sql/adobe_mx_query.sql', 'r').read()
    us_query_template = open('sql/us_query.sql', 'r').read()

    agi_query_generator = QueryGenerator(agi_query_template, 'agi')
    mx_query_generator = QueryGenerator(mx_query_template, 'adobe_mx')
    us_query_generator = QueryGenerator(us_query_template, 'us')

    query_generators = [agi_query_generator, mx_query_generator, us_query_generator]

    for start_date in start_dates:

        start_date_str = start_date.strftime("%Y-%m-%d")
        logging.info(f"Date {start_date_str} - start processing")

        for generator in query_generators:
            generator.generate_query_for_dates(start_date)

            logging.info(f"Date {start_date_str} - retrieving clickstream data for {generator.country}")
            command = _build_snowsql_command(generator, snowflake_account, start_date_str)
            command.run(dry_run=dry_run)

            if upload:
                logging.info(f"Date {start_date_str} - uploading clickstream data for {generator.country} to s3")
                command = _build_s3_command(generator, start_date_str, upload_path, aws_profile)
                command.run(dry_run=dry_run)

            logging.debug(f"Date {start_date_str} - cleaning up SQL query for {generator.country}")
            #generator.cleanup(start_date_str)

            logging.debug(f"Date {start_date_str} - completed processing for {generator.country}")

        logging.info(f"Date {start_date_str} - completed processing")
