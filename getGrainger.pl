#!/usr/bin/perl

use strict;
use URI::Escape;

my $header_file = "/local/search/data/interactions/adobe/column_headers.tsv";
my $tmpStr;
my @tmpArr;
my %headers;
my $numFields;
my $i = 0;
my $product;
my $keyword;
my $productKeyword;
my $date;
my $customer;
my $referrer;
my $url;
my $referral;
my $pageName;
my $productList;
my %carts = ();
my $CART_ADD = 12;
my $CART_PURCHASE = 1;
my %parent = ();
my %webParent = ();
my $parent_file = "/local/search/data/items/en-parent_child.txt";
my $file;
my $sapID;
my $sapAcc;
my $demandBase;
my $industry;
my $subIndustry;
my $audience;
my $audienceSegment;
my %childSKUMap;

sub loadHeaders() {
   open(HEADERS, "<", "$header_file") or die "cannot open < $header_file: $!";
   $tmpStr = <HEADERS>;
   close(HEADERS);

   chomp($tmpStr);
   @tmpArr = split(/\t/,$tmpStr,-1);
   foreach my $k (@tmpArr) {
      $headers{$k} = $i++;
   }
   $numFields = scalar(keys %headers);
}

sub loadParents() {
   my $child;
   my $parent;
   my $webParent;

   open(PARENT, "<", "$parent_file") or die "cannot open < $parent_file: $!";
   while(<PARENT>) {
      if(/^child_material_no\|/) {
         $child = $_;
         $child =~ s/[\r\n]+$//;
         $child =~ s/^child_material_no\|//;
      }
      elsif(/^parent_material_no\|/) {
         $parent = $_;
         $parent =~ s/[\r\n]+$//;
         $parent =~ s/^parent_material_no\|//;
         if($child ne $parent) {
            $parent{lc($child)} = lc($parent);
         }
      }
      elsif(/^web_parent_material_no\|/) {
         $webParent = $_;
         $webParent =~ s/[\r\n]+$//;
         $webParent =~ s/^web_parent_material_no\|//;
         if($child ne $webParent) {
            $webParent{lc($child)} = lc($webParent);
             if (!exists $childSKUMap{lc $webParent}) {
              $childSKUMap{lc $webParent} = lc $child;
            }
         }
      }
   }
}

sub loadCarts() {
   my $product;
   my $action;
   my $actions;
   my $data;
   open(CART, "<", "$file") or die "cannot open < $file: $!";
   while(<CART>) {
      chomp;
      @tmpArr = split(/\t/,$_,-1);
      if(scalar(@tmpArr) != $numFields) {
         # Invalid data row from Adobe
         next;
      }
      $actions = $tmpArr[$headers{'event_list'}];
      $customer = lc($tmpArr[$headers{'post_visid_high'}])."_".lc($tmpArr[$headers{'post_visid_low'}]);
      $data = lc($tmpArr[$headers{'product_list'}]);
      $product = '';
      $action = '';
      
      #;3C591;;;218=3|219=80.4|220=80.4;eVar39=ST|eVar40=PDPD
      if($actions =~ m/^$CART_ADD,|,$CART_ADD$|,$CART_ADD,|^$CART_ADD$/) {
         if($data =~ m/^;(.*?);.*/) {
             $action = $CART_ADD;
             $product = $1;
         }
      }

      #;1GEK3;2;93.6,;2LJJ2;2;43.4,;2LJG9;2;54.3
      if($actions =~ m/^$CART_PURCHASE,|,$CART_PURCHASE$|,$CART_PURCHASE,|^$CART_PURCHASE$/) {
         if($data =~ m/^;(.*?);.*/) {
             $action = $CART_PURCHASE;
             $product = $1;
         }
      }

      if($product ne '' && $action ne '' && $customer ne '') {
         $carts{$customer}{$product} = $action;
         if(defined($parent{$product})) {
            $carts{$customer}{$parent{$product}} = $action;
         }
         if(defined($webParent{$product})) {
            $carts{$customer}{$webParent{$product}} = $action;
         }
      }
   }
   close(CART);
}

sub cleanKeyword($) {
   my $url = shift;

         # Grainger search result page.  Order here is important.  For example, there could be a redirect to an L3 with a keyword refinement.  The redirect needs to take precedence over the Ntt.
         if($url =~ /searchquery=([^&#]+)/) {
            $keyword = $1;
         }  
         elsif($url =~ /okey=([^&#]+)/) {
            $keyword = $1;
         }  
         elsif($url =~ /redirect=([^&#]+)/) {
            $keyword = $1;
         }  
         elsif($url =~ /ntt-(.*?)[\?\/]/) {
            $keyword = $1;
         }
         else {
            $keyword = '';
         }  
   
         $keyword = uri_unescape($keyword);
         $keyword =~ s/\|.*//;
         if($keyword =~ /^\d[^ ]+\d$/) {
            # Skip item number searches
            $keyword = '';
         }
         if($keyword =~ /img|http|https|=/) {
            $keyword = '';
         }
         if($keyword =~ /[^[:print:]]/) {
            $keyword = '';
         }
         $keyword =~ s/&quot;/"/g;
         if($keyword =~ /^"(.*)"$/) {
            $keyword = $1;
         }
         $keyword =~ s/\|/ /g;
         $keyword =~ s/,/ /g;
         $keyword =~ s/\\/ /g;
         $keyword =~ s/\+/ /g;
         $keyword =~ s/[ ]+/ /g;
         $keyword =~ s/^[ ]//g;
         $keyword =~ s/[ ]$//g;
    return $keyword;
}

sub mineEvents() {
   open(FILE, "<", "$file") or die "cannot open < $file: $!";
   	my $childSKURef;
    my $webparent;
   while(<FILE>) {
      chomp;
      @tmpArr = split(/\t/,$_,-1);
      if(scalar(@tmpArr) != $numFields) {
         # Invalid data row from Adobe
         next;
      }
      $url = lc($tmpArr[$headers{'page_url'}]);
      $referrer = lc($tmpArr[$headers{'post_referrer'}]);
      $pageName = lc($tmpArr[$headers{'pagename'}]);
      $productList = lc($tmpArr[$headers{'product_list'}]);
      $keyword = '';
      $childSKURef = '';
      $webparent = '';
      #
      # Strip enclosing " from $url and $referrer"
      $url =~ s/^"//; $url =~ s/"$//;
      $referrer =~ s/^"//; $referrer =~ s/"$//;
      $productList =~ s/^"//; $productList =~ s/"$//;


      # Check referrer in when going from results to IDP
      $referrer =~ s/#.*//; # Avoid false matches in client-side url
      if(($referrer =~ /^http[s]?:\/\/www\.grainger\.com\/search/ || $referrer =~ /^http[s]?:\/\/www\.grainger\.com\/category/) && ($referrer =~ /searchquery=[^&#]+/ || $referrer =~ /searchQuery=[^&#]+/ || $referrer =~ /redirect=[^&#]+/ || $referrer =~ /okey=[^&#]+/ || $referrer =~ /ntt-.*?[\?\/]/)) {
         $keyword = cleanKeyword($referrer);
      }
      # Check url for mini-IDP events
      $url =~ s/#.*//; # Avoid false matches in client-side url
      if(($url =~ /^http[s]?:\/\/www\.grainger\.com\/search/ || $url =~ /^http[s]?:\/\/www\.grainger\.com\/category/) && ($url =~ /searchquery=[^&#]+/ || $url =~ /searchquery=[^&#]+/ || $url =~ /redirect=[^&#]+/ || $url =~ /okey=[^&#]+/ || $url =~ /ntt-.*?[\?\/]/)) {
         $keyword = cleanKeyword($url);
      }

      if(($url =~ /\/product\// || $pageName =~ /mini idp/ || ($pageName =~ /add to cart/ && $productList =~ /evar40=lves/))
             && $url !~ /\/compare/ && $keyword ne "::empty::" && $keyword ne "-" && $keyword ne "" && $keyword !~ /grainger/) {
         $product = $url;
         $product =~ s/#.*//;

         # Skip sku search events that happen immediately following search results.  The product page is not related to the keyword search.
         $productKeyword = $keyword;
         if(($url =~ /\/product\//) && ($product =~ /^.*?searchquery=([^&#]+).*?$/ || $product =~ /^.*?searchQuery=([^&#]+).*?$/)) { # https://www.grainger.com/product/eemax-electric-tankless-water-heater-21ht82?nls=3&nlsit=0.8&ssf=3&searchquery=21ht82
            $productKeyword = cleanKeyword($product);
         }
         if($keyword ne $productKeyword) {
            next;
         }
         
         # Quick-view and Mini-IDP calls on search results pages.
         # Pickup mini-idp actions for Table View pages and ATC actions for List View since mini-idp was removed from that page.
         # Both ATC and purchase amplification come later in the code.
         if(($pageName =~ /mini idp/ || $pageName =~ /add to cart/) && $productList ne "") {
            $product = $productList;
            $product =~ s/^;//;
            $product =~ s/;.*//;
            # Skip if this does not look like a product
            if($product !~ /^\d[^ ]+\d$/) {
               $product = "";
            }
         }

         $product =~ s/\?.*$//;
         $product =~ s/^.*\/product\///;
         $product =~ s/^[^\/]*\-//;
         $product =~ s/\/.*//;
         $product =~ s/^http.*//; # Throw this event away if we get here and this matches

         $referral = "grainger";
         $date = lc($tmpArr[$headers{'date_time'}]);
         $customer = lc($tmpArr[$headers{'post_visid_high'}])."_".lc($tmpArr[$headers{'post_visid_low'}]);
   
         $sapID = lc($tmpArr[$headers{'post_evar19'}]);
	 $sapAcc = lc($tmpArr[$headers{'post_evar20'}]);
	 $demandBase = lc($tmpArr[$headers{'post_evar11'}]);
	 $industry = '';
	 $subIndustry = '';
	 $audience = '';
	 $audienceSegment = '';

	 # 14445400:Balfour Beatty Capital Group Inc:COMMERCIAL SERVICES:FINANCIAL:Enterprise:Over $5B:Enterprise Business:Financial Services
	 if(defined $demandBase and length $demandBase) {
		my @demandBaseInfo = split(':', $demandBase, -1);
                # -1 for empty values 
		if (defined $demandBaseInfo[0] and length $demandBaseInfo[0] and $demandBaseInfo[0] ne "[n/a]" ) {
			$industry = $demandBaseInfo[2];
         		$subIndustry = $demandBaseInfo[3];
         		$audience = $demandBaseInfo[6];
         		$audienceSegment = $demandBaseInfo[7];
		}			
         }

         # If valid product and keyword is not a simple product lookup
         if ($product ne "" && $keyword ne $product) {
         	$childSKURef = $product;
         	if ($product =~ /^wp/) {
              $childSKURef = $childSKUMap{$product};
            }
	    ## Strip enclosing " for the below fields
	    #
	    $childSKURef = $product;
            $date =~ s/^"//; $date =~ s/"$//;
            $sapAcc =~ s/^"//; $sapAcc =~ s/"$//;
            $industry =~ s/^"//; $industry =~ s/"$//;
            $subIndustry =~ s/^"//; $subIndustry =~ s/"$//;
            if(defined($carts{$customer}{$product}) && $carts{$customer}{$product} eq $CART_PURCHASE) {
               # PURCHASE event
               print "$keyword,$product,$date,$customer,0,0,1,,$sapID,$sapAcc,$industry,$subIndustry,$audience,$audienceSegment,$childSKURef\n";
            }
            elsif(defined($carts{$customer}{$product}) && $carts{$customer}{$product} eq $CART_ADD) {
               # ATC event
               print "$keyword,$product,$date,$customer,0,1,0,,$sapID,$sapAcc,$industry,$subIndustry,$audience,$audienceSegment,$childSKURef\n";
            }
            else {
               # VIEW event
               print "$keyword,$product,$date,$customer,1,0,0,,$sapID,$sapAcc,$industry,$subIndustry,$audience,$audienceSegment,$childSKURef\n";
            }
         }
      }
   }
   close(FILE);
}
   
$file = $ARGV[0];
loadParents();
loadHeaders();
loadCarts();
mineEvents();
exit(0);
   