#!/usr/bin/perl

use strict;
use URI::Escape;

my $header_file = "/local/search/data/interactions/adobe/column_headers.tsv";
my $tmpStr;
my @tmpArr;
my %headers;
my $i = 0;
my $product;
my $keyword;
my $referral;
my $date;
my $customer;
my $pageNum;

open(HEADERS, "<", "$header_file") or die "cannot open < $header_file: $!";
$tmpStr = <HEADERS>;
close(HEADERS);

chomp($tmpStr);
@tmpArr = split(/\t/,$tmpStr);
foreach my $k (@tmpArr) {
   $headers{$k} = $i++;
}

while(<>) {
   chomp;
   @tmpArr = split /\t/;
   $product = lc($tmpArr[$headers{'page_url'}]);
   $keyword = lc(uri_unescape($tmpArr[$headers{'visit_keywords'}]));
   $pageNum = lc($tmpArr[$headers{'visit_page_num'}]);
   if($pageNum == 1 && $product =~ /^http[s]?:\/\/www\.grainger\.com\/product/ && $product !~ /\/compare/ && $keyword ne "::empty::" && $keyword ne "-" && $keyword ne "" && $keyword !~ /grainger/) {
      $product =~ s/#.*//;
      $product =~ s/\?.*$//;
      $product =~ s/^.*\/product\///;
      $product =~ s/^[^\/]*\-//;
      $product =~ s/\/.*//;
      $referral = $tmpArr[$headers{'visit_ref_domain'}];
      #$referral = $tmpArr[$headers{'post_referrer'}];
      if(!defined($referral)) {
        $referral = "Unknown";
      }
      $date = lc($tmpArr[$headers{'date_time'}]);
      $customer = lc($tmpArr[$headers{'post_visid_high'}])."_".lc($tmpArr[$headers{'post_visid_low'}]);

      $keyword =~ s/\|/ /g;
      $keyword =~ s/,/ /g;
      $keyword =~ s/\\/ /g;
      $keyword =~ s/\+/ /g;
      $keyword =~ s/[ ]+/ /g;
      $keyword =~ s/^[ ]//g;
      $keyword =~ s/[ ]$//g;

      # If the keyword is a product search then ignore the event
      if ($keyword ne $product) {
        print "$keyword,$product,$date,$customer,1,0,0,$referral,\n";
      }
   }
}
