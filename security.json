{"authentication": {"blockUnknown": false, "class": "solr.BasicAuthPlugin", "credentials": {"solr-admin": "CQGa7Dr7ST1vEp7MIUPflkaepdp05SgEOK2WfSCSqs4= wX6nd4VsaYl/XETW8+oSNiC5x7H/Lc93aigGvzFIkQ0=", "solr-updater": "q6TirDOoQmkdLB+x34fcWNE+d0JD4Zg3kV9no+zTjRE= CpY79WT4yfS0TvuXj0qcs7Tf2vaF0G9/Z2ECLE40Amc=", "solr-reader": "aT0Xp+deqlJ3szrrBrF39347AUDKm9IJOaMed9GGtfs= aTCnxcJ2OX/WTDqWBHncUmv69neiuV4jx+u1D6LqpZg="}}, "authorization": {"class": "solr.RuleBasedAuthorizationPlugin", "user-role": {"solr-admin": ["admin", "update"], "solr-updater": "update", "solr-reader": "read", "solr-ro": "read"}, "permissions": [{"name": "gww-grit-environment-read", "path": "/gritEnvironments", "role": null, "index": 1}, {"name": "update", "role": ["admin", "update"], "index": 2}, {"name": "gww-orders-search-api", "path": ["/select", "/query", "/export", "/spell", "/ordersearch", "/ppisearch", "/pppsearch", "/get"], "collection": ["orders", "items4orders", "items4orders_bak"], "role": ["read-orders", "index-orders", "update", "admin"], "index": 3}, {"name": "gww-orders-index-api", "path": ["/update", "/replication"], "collection": ["orders"], "role": ["index-orders", "update", "admin"], "index": 4}, {"name": "gww-interactions-read", "path": ["/select", "/query", "/get", "/categoryPrediction", "/keepstock", "/orders", "/items", "/items_attrs", "/list", "/address", "/spell"], "collection": ["interactions", "interactions_bak"], "role": ["read", "read-stage", "update", "admin"], "index": 5}, {"name": "gww-items-read", "path": ["/select", "/query", "/get", "/idp", "/gritFacetTerms", "/browse", "/terms", "/clustering", "/productsCarousel", "/productFinder", "/blacklist", "/page"], "collection": ["items", "items_main", "items_bak", "items_bot", "items_beta", "items_beta_main", "items_beta_bak", "items_attrs", "items_attrs_main", "items_attrs_bak", "items_xref"], "role": ["read", "read-items", "update", "admin"], "index": 6}, {"name": "gww-unified-items-read", "path": ["/select", "/query", "/get", "/idp", "/gritFacetTerms", "/browse", "/terms", "/clustering", "/productsCarousel", "/productFinder", "/blacklist", "/searchXREF", "/searchXREFCLICKML", "/searchXREFTier", "/searchXREFCLICKTier", "/list", "/address", "/page"], "collection": ["items", "items_main", "items_bak", "items_bot", "items_beta", "items_beta_main", "items_beta_bak", "items_attrs", "items_attrs_main", "items_attrs_bak", "items_xref"], "role": ["read", "read-items", "update", "admin"], "index": 7}, {"name": "gww-items-stage-read", "path": ["/select", "/query", "/get", "/idp", "/gritFacetTerms", "/browse", "/terms", "/clustering", "/productsCarousel", "/productFinder", "/page", "/blacklist"], "collection": ["items_stage"], "role": ["read", "read-stage", "update", "admin"], "index": 8}, {"name": "gww-grit-secured", "path": ["/gritConfigs", "/gritFields", "/gritPages", "/gritRules", "/gritSession", "/gritUsers"], "collection": ["items", "items_bak", "items_stage", "items_bot"], "role": null, "index": 9}, {"name": "gww-ip-filter-replication", "path": "/replication", "role": null, "index": 10}, {"name": "gww-customer-read", "path": ["/select", "/query", "/get", "/categoryPrediction", "/keepstock", "/orders", "/items", "/list", "/address", "/spell"], "collection": ["csp_quotes_customer_partner", "csp_quotes_header", "csp_quotes_header_adtl", "csp_quotes_line_item", "interactions_bot", "invoices", "keepstock", "oms_notifications", "orders", "ship_notifications", "typeahead_address", "typeahead_fieldoptions", "typeahead_list"], "role": ["read", "update", "admin"], "index": 11}, {"name": "gww-all-admin", "collection": null, "path": "/*", "role": ["admin", "update"], "index": 12}, {"name": "gww-all-core", "path": "/*", "role": ["admin", "update"], "index": 13}]}}