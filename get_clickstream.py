# python

# Example usage:
# args = parse_args()
# print("Date:", args.date)
# print("Country:", args.country)
#
# # Example SQL query
# sql = f"SELECT * FROM PUBLISH.DEA.HITS_DAILY_EDV WHERE TO_DATE(date_time) = '{args.date}' AND username = '{args.country}' LIMIT 5"
#
# # Connect and fetch data
# snow_conn = snowflake.connector.connect(
#     user=user,
#     password=password,
#     account=account,
#     warehouse="SEARCH_ENGINE_WH_M",
#     database="SEARCH_ENGINE",
#     schema="PUBLIC"
# )
# cur = snow_conn.cursor()
# try:
#     df = execute_sql_get_data(cur, sql)
#     print(df)
# finally:
#     cur.close()
#     snow_conn.close()
#    

import os
import sys
import pandas as pd
import argparse
from cryptography.hazmat.primitives import serialization
import snowflake.connector

# Snowflake credentials (from env)
user = os.environ['SNOWSQL_USER']
password = os.environ['SNOWSQL_PWD']
account = os.environ['SNOWFLAKE_ACCOUNT']
#account = 'wwgrainger.us-east-1'
destination_s3_path = os.environ['SNOW_DESTINATION_S3_PATH']

country_to_username = {
  "mx": "wwgraingergcommx",
  "us": "wwgraingerglobalprod",
  "agi": "wwgraingeracklands",
  # Add more mappings as needed
}

def execute_sql_get_data(cur, sql):
    cur.execute(sql)
    return pd.DataFrame.from_records(cur.fetchall(), columns=[i[0] for i in cur.description])

def execute_sql(cur, sql):
    cur.execute(sql)

def parse_args():
  parser = argparse.ArgumentParser(description="Snowflake data extraction script")
  parser.add_argument("--date", required=True, help="Date in YYYY-MM-DD format")
  parser.add_argument("--country", required=True, help="Country (e.g., wwgraingeracklands)")
  return parser.parse_args()


def main():
  args = parse_args()
  run_date = args.date
  country = args.country

  print("Python version:", sys.version)
  print("Snowflake version:", snowflake.connector.__version__)
  print("Username:", user)
  print("Passwd:",password)
  print("Account:",account)
  print("Date:", run_date)
  print("Country:", country)


  # Load private key
  private_key_path = Path(os.environ['SNOWSQL_PRIVATE_KEY_PATH']).expanduser()

  # === Load private key and convert to the format Snowflake connector expects ===
  pem_data = private_key_path.read_bytes()

  with open(private_key_path, "rb") as key:
      p_key = key.read()
  
  snow_conn = snowflake.connector.connect(
      user=user,
      account=account,
      private_key_file=private_key_path,
      warehouse="SEARCH_ENGINE_WH_M",
      database="SEARCH_ENGINE",
      schema="PUBLIC"
  )

  cur = snow_conn.cursor()
  additional_fields = ""
  language_field = "evar62, "
  if(country == "us"):
    additional_fields = ",visit_keywords,visit_page_num,visit_ref_domain"
    language_field = ""
  sql_unload = f"""
  COPY INTO @{destination_s3_path}/{country}/{run_date}/
  FROM (
    select
      event_list,
      post_visid_high,
      post_visid_low,
      product_list,
      page_url,
      post_referrer,
      pagename,
      {language_field}
      to_char(TO_TIMESTAMP_NTZ(date_time), 'YYYY-MM-DD HH24:MI:SS') as date_time,
      coalesce(post_evar19, '') as post_evar19,
      coalesce(post_evar20, '') as post_evar20,
      coalesce(post_evar11, '') as post_evar11
      {additional_fields}
    from PUBLISH.DEA.HITS_DAILY_EDV
    where
      TO_TIMESTAMP_NTZ(date_time) >= TO_TIMESTAMP_NTZ('{run_date} 00:00:00')
      and TO_TIMESTAMP_NTZ(date_time) < TO_TIMESTAMP_NTZ('{run_date} 23:59:59')
      and username in ('{country_to_username[country]}')
    order by date_time asc
  )
  FILE_FORMAT = (format_name = 'SEARCH_ENGINE.FILE_FORMAT.SEARCH_TSV')
  OVERWRITE = TRUE;
  """
  
  print(sql_unload)
  try:
      df = execute_sql_get_data(cur, sql_unload)
      # handle df if needed, e.g. print(df.head())
#      print(df.head())
  finally:
      cur.close()
      snow_conn.close()

if __name__ == "__main__":
    main()

