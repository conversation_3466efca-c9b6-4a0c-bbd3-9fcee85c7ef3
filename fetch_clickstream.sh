#!/usr/bin/env bash
  
start_date=$1
end_date=$2
country=$3

current_date="$start_date"
while [[ "$current_date" < "$end_date" || "$current_date" == "$end_date" ]]; do
    echo "Fetching clickstream data for $current_date for $country"
    python3.6 get_clickstream.py --date "$current_date" --country "$country"
    current_date=$(date -I -d "$current_date + 1 day")
done


current_date="$start_date"
while [[ "${current_date}" < "${end_date}" || "$current{_date" == "${end_date}" ]]; do
    echo "Downloading clickstream data for $current_date for $country"
    ./download_data_from_s3.sh ${current_date} ${country}
    current_date=$(date -I -d "${current_date} + 1 day")
done

pushd /local/search/solrBuilders/solrInteractionBuilder
current_date="$start_date"
while [[ "${current_date}" < "${end_date}" || "$current{_date" == "${end_date}" ]]; do
    echo "Parsing clickstream data for $current_date for $country"
    ./t_fetchGrainger.sh ${country} ${current_date}
    current_date=$(date -I -d "${current_date} + 1 day")
done
popd
