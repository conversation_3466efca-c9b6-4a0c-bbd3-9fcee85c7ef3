# solr-clickstream-retrieve

## Dependencies

This was developed on python3 3.9 but should be able to run on lower python3 versions with some tweaks.

Tools that need to be installed
- [snowsql](https://docs.snowflake.com/en/user-guide/snowsql-install-config)
- aws CLI

Env vars
- SNOWSQL_USER
- SNOWSQL_PWD
export SNOWSQL_USER=SEARCH_ENGINE_SVC
export SNOWSQL_PWD=Q8dw4ENX3vI1CzVw
export SNOWFLAKE_ACCOUNT=wwgraingerdev.us-east-1
## How to use & what to expect

snowsql -a wwgraingerdev.us-east-1 -u SEARCH_ENGINE_SVC


There are multiple parameters supported, see `python3 get_clickstream.py --help`. This section only covers the basic usage.

`python3 get_clickstream.py <start_date> <end_date_exclusive>`
python3 get_clickstream.py  <end_date_exclusive>

This script generates 1 clickstream file per date, per country, in `./out/<country>/`.

For this example run:

`python3 get_clickstream.py 2024-06-01 2024-06-02`
python3 get_clickstream.py 2025-01-01 2025-01-31
python3 get_clickstream.py --range-start 2025-01-01 --range-end 2025-01-31
python3 get_clickstream.py --range-start 2025-03-01 --range-end 2025-03-02
python3.13 get_clickstream.py --range-start 2025-03-01 --range-end 2025-03-02
python3.9 get_clickstream_new.py --range-start 2025-03-01 --range-end 2025-03-02

Output:

```
$ ls ./out/agi
clickstream-headers.tsv
clickstream-2024-06-01.tsv

$ ls ./out/us
clickstream-headers.tsv
clickstream-2024-06-01.tsv


sudo dnf install python3.11
sudo dnf install python3.11-pip
python3.11 --version
python3.11 -m venv interaction_builder
source interaction_builder/bin/activate


```

*It is the caller's responsibility to clean up retrieved clickstream files in `./out/<country>/`.*

python3 get_clickstream.py 2025-03-01 2025-03-31
## Examples

Default: execute (not dry run) mode, without s3 upload (i.e. download only)

`python3 get_clickstream.py 2025-03-01 2024-03-31`

Execute (not dry run) mode, including uploading to s3

`python3 get_clickstream.py 2024-06-01 2024-06-05 --upload`

Execute (not dry run) mode, including uploading to s3, with the chosen AWS profile

`python3 get_clickstream.py 2024-06-01 2024-06-05 --upload --aws-profile <profile_name>`

Dry-run, with s3 upload

`python3 get_clickstream.py 2024-06-01 2024-06-05 --dry-run --upload`

## Logging config

Logging config such as log level, rotating file handler (for rolling logs), write mode are configured in the first few lines of the script.

features/SRCHENH-2523


----

scp -C -rp adobe_mx/* qal1solap500.qaaws.grainger.com:/home/<USER>/out/adobe_mx/
scp -C -rp agi/* qal1solap500.qaaws.grainger.com:/home/<USER>/out/adobe_agi/
scp -C -rp us/* qal1solap500.qaaws.grainger.com:/home/<USER>/out/adobe/

scp -C -rp us/clickstream-2024-09-17.tsv qal1solap500.qaaws.grainger.com:/home/<USER>/out/adobe/
scp -C -rp us/clickstream-2024-09-18.tsv qal1solap500.qaaws.grainger.com:/home/<USER>/out/adobe/
scp -C -rp us/clickstream-2024-09-19.tsv qal1solap500.qaaws.grainger.com:/home/<USER>/out/adobe/
scp -C -rp us/clickstream-2024-09-09.tsv qal1solap500.qaaws.grainger.com:/home/<USER>/out/adobe/


scp /local/search/out/us_events/grainger*.event qal2solap500:/local/search/data/interactions/
scp /local/search/out/mx_events/*.event qal2solap500:/local/search/data/interactions/
scp /local/search/out/agi_events/*.event qal2solap500:/local/search/data/interactions/


scp /local/search/out/us_events/grainger*.event q2lsolap500:/local/search/data/interactions/
scp /local/search/out/mx_events/*.event q2lsolap500:/local/search/data/interactions/
scp /local/search/out/agi_events/*.event q2lsolap500:/local/search/data/interactions/

scp /local/search/out/us_events/grainger*.event ppl0solap500:/local/search/data/interactions/
scp /local/search/out/mx_events/*.event ppl0solap500:/local/search/data/interactions/
scp /local/search/out/agi_events/*.event ppl0solap500:/local/search/data/interactions/

scp -C qal1solap500.qaaws.grainger.com:/local/search/out/us_events/*.event prl2solap500.prodaws.grainger.com:~/interactions/
scp -C qal1solap500.qaaws.grainger.com:/local/search/out/agi_events/*.event prl2solap500.prodaws.grainger.com:~/interactions/
scp -C qal1solap500.qaaws.grainger.com:/local/search/out/mx_events/*.event prl2solap500.prodaws.grainger.com:~/interactions/


DATE=2025-02-01
for i in {0..25}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh us $NEXT_DATE ; done

for i in {0..31}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh agi $NEXT_DATE ; done

for i in {0..31}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh mx $NEXT_DATE ; done


for i in {0..9}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh mx $NEXT_DATE;    ./t_fetchGrainger.sh agi $NEXT_DATE ; done

for i in {0..9}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh agi $NEXT_DATE ; done

for i in {0..9}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh mx $NEXT_DATE;    ./t_fetchGrainger.sh us $NEXT_DATE ; done

for i in {0..9}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh mx $NEXT_DATE ; done

DATE=2025-09-06
for i in {0..3}; do    NEXT_DATE=$(date +%Y-%m-%d -d "$DATE + $i day");    echo "$NEXT_DATE";    ./t_fetchGrainger.sh mx $NEXT_DATE;  done