#!/usr/bin/perl

use strict;
use URI::Escape;

# Use the existing headers file
my $header_file = "out/us/clickstream-headers.tsv";
my $tmpStr;
my @tmpArr;
my %headers;
my $numFields;
my $i = 0;
my $product;
my $keyword;
my $productKeyword;
my $date;
my $customer;
my $referrer;
my $url;
my $referral;
my $pageName;
my $productList;
my %carts = ();
my $CART_ADD = 12;
my $CART_PURCHASE = 1;
my %parent = ();
my %webParent = ();
my $file;
my $sapID;
my $sapAcc;
my $demandBase;
my $industry;
my $subIndustry;
my $audience;
my $audienceSegment;
my %childSKUMap;

sub loadHeaders() {
   open(HEADERS, "<", "$header_file") or die "cannot open < $header_file: $!";
   $tmpStr = <HEADERS>;
   close(HEADERS);

   chomp($tmpStr);
   @tmpArr = split(/\t/,$tmpStr,-1);
   foreach my $k (@tmpArr) {
      $headers{$k} = $i++;
   }
   $numFields = scalar(keys %headers);
}

# Skip loading parents and carts for this test
sub loadParents() {
   # Empty for test
}

sub loadCarts() {
   # Empty for test - no cart data to process
}

sub cleanKeyword($) {
   my $url = shift;

         # Grainger search result page.  Order here is important.  For example, there could be a redirect to an L3 with a keyword refinement.  The redirect needs to take precedence over the Ntt.
         if($url =~ /searchquery=([^&#]+)/i) {
            $keyword = $1;
         }  
         elsif($url =~ /okey=([^&#]+)/i) {
            $keyword = $1;
         }  
         elsif($url =~ /redirect=([^&#]+)/i) {
            $keyword = $1;
         }  
         elsif($url =~ /ntt-(.*?)[\?\/]/i) {
            $keyword = $1;
         }
         else {
            $keyword = '';
         }  
   
         $keyword = uri_unescape($keyword);
         $keyword =~ s/\|.*//;
         if($keyword =~ /^\d[^ ]+\d$/) {
            # Skip item number searches
            $keyword = '';
         }
         if($keyword =~ /img|http|https|=/) {
            $keyword = '';
         }
         if($keyword =~ /[^[:print:]]/) {
            $keyword = '';
         }
         $keyword =~ s/&quot;/"/g;
         if($keyword =~ /^"(.*)"$/) {
            $keyword = $1;
         }
         $keyword =~ s/\|/ /g;
         $keyword =~ s/,/ /g;
         $keyword =~ s/\\/ /g;
         $keyword =~ s/\+/ /g;
         $keyword =~ s/[ ]+/ /g;
         $keyword =~ s/^[ ]//g;
         $keyword =~ s/[ ]$//g;
    return $keyword;
}

sub mineEvents() {

   open(FILE, "<", "$file") or die "cannot open < $file: $!";
   	my $childSKURef;
    my $webparent;
   while(<FILE>) {
      chomp;
      @tmpArr = split(/\t/,$_,-1);
      if(scalar(@tmpArr) != $numFields) {
         # Invalid data row from Adobe
         print STDERR "mineEvents1::Invalid data row from Adobe: $numFields, " . scalar(@tmpArr) . ".\n";
         next;
      }
      $url = lc($tmpArr[$headers{'page_url'}]);
      $referrer = lc($tmpArr[$headers{'post_referrer'}]);
      $pageName = lc($tmpArr[$headers{'pagename'}]);
      $productList = lc($tmpArr[$headers{'product_list'}]);
      
      # Strip quotes from field values
      $url =~ s/^"//; $url =~ s/"$//;
      $referrer =~ s/^"//; $referrer =~ s/"$//;
      $pageName =~ s/^"//; $pageName =~ s/"$//;
      $productList =~ s/^"//; $productList =~ s/"$//;
      
      $keyword = '';
      $childSKURef = '';
      $webparent = '';
      # Check referrer in when going from results to IDP
      $referrer =~ s/#.*//; # Avoid false matches in client-side url
      if(($referrer =~ /^http[s]?:\/\/www\.grainger\.com\/search/ || $referrer =~ /^http[s]?:\/\/www\.grainger\.com\/category/) && ($referrer =~ /searchquery=[^&#]+/i || $referrer =~ /redirect=[^&#]+/i || $referrer =~ /okey=[^&#]+/i || $referrer =~ /ntt-.*?[\?\/]/i)) {
         $keyword = cleanKeyword($referrer);
      }

      if(($url =~ /^http[s]?:\/\/www\.grainger\.com\/search/ || $url =~ /^http[s]?:\/\/www\.grainger\.com\/category/) && ($url =~ /searchquery=[^&#]+/i || $url =~ /redirect=[^&#]+/i || $url =~ /okey=[^&#]+/i || $url =~ /ntt-.*?[\?\/]/i)) {
         $keyword = cleanKeyword($url);
      }

      print STDERR "DEBUG: URL='$url', Referrer='$referrer', Keyword='$keyword'\n";

      if(($url =~ /\/product\// || $pageName =~ /mini idp/ || ($pageName =~ /add to cart/ && $productList =~ /evar40=lves/))
             && $url !~ /\/compare/ && $keyword ne "::empty::" && $keyword ne "-" && $keyword ne "" && $keyword !~ /grainger/) {
         $product = $url;
         $product =~ s/#.*//;

         # Skip sku search events that happen immediately following search results.  The product page is not related to the keyword search.
         $productKeyword = $keyword;
         if(($url =~ /\/product\//) && ($product =~ /^.*?searchquery=([^&#]+).*?$/)) { # https://www.grainger.com/product/eemax-electric-tankless-water-heater-21ht82?nls=3&nlsit=0.8&ssf=3&searchquery=21ht82
            $productKeyword = cleanKeyword($product);
         }

         $product =~ s/\?.*$//;
         $product =~ s/^.*\/product\///;
         $product =~ s/^[^\/]*\-//;
         $product =~ s/\/.*//;
         $product =~ s/^http.*//; # Throw this event away if we get here and this matches

         $referral = "grainger";
         $date = lc($tmpArr[$headers{'date_time'}]);
         $customer = lc($tmpArr[$headers{'post_visid_high'}])."_".lc($tmpArr[$headers{'post_visid_low'}]);
   
         $sapID = lc($tmpArr[$headers{'post_evar19'}]);
	 $sapAcc = lc($tmpArr[$headers{'post_evar20'}]);
	 $demandBase = lc($tmpArr[$headers{'post_evar11'}]);
	 
	 # Strip quotes from field values
	 $date =~ s/^"//; $date =~ s/"$//;
	 $sapID =~ s/^"//; $sapID =~ s/"$//;
	 $sapAcc =~ s/^"//; $sapAcc =~ s/"$//;
	 $demandBase =~ s/^"//; $demandBase =~ s/"$//;
	 
	 $industry = '';
	 $subIndustry = '';
	 $audience = '';
	 $audienceSegment = '';

         print STDERR "DEBUG: Processing product='$product', keyword='$keyword'\n";

         # If valid product and keyword is not a simple product lookup
         if ($product ne "" && $keyword ne $product) {
         	$childSKURef = $product;
         	if ($product =~ /^wp/) {
              $childSKURef = $childSKUMap{$product};
            }
            # Always output VIEW event for test (no cart data loaded)
            print "$keyword,$product,$date,$customer,1,0,0,,$sapID,$sapAcc,$industry,$subIndustry,$audience,$audienceSegment,$childSKURef\n";
         }
      }
   }
   close(FILE);
}
   
$file = $ARGV[0];
loadParents();
loadHeaders();
loadCarts();
mineEvents();
exit(0);
