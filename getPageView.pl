#!/usr/bin/perl -w

#########################################################
# This script will stitch together search results pages
# and product pages in the CoreMetrics file PageView.txt
# by cookie+session to relate keywords to products.
#
# The events within a session are not in order of time.
# Therefore we need to keep session information in
# memory and are not able to purge data as we hit events.
#
# Load cart events for reference from memory.  Match
# child or parent events. Code 5 is an add and 9 is a
# purchase.
#########################################################

use Time::Local;

use POSIX 'strftime';
my $i=0;
my @cols;
my $page;
my $date;
my ($mday,$mon,$year,$hour,$min,$sec);
my $time;
my $cookie;
my $session;
my $url;
my $keyword;
my $product;
my %sessions = ();
my $customer;
my $eventKeyword;
my $eventProduct;
my %carts = ();
my $cartFile = "/local/search/data/interactions/coremetrics/ShoppingCart.txt";
my $CART_ADD = 5;
my $CART_PURCHASE = 9;
my %parent = ();
my %webParent = ();
my $parent_file = "/local/search/data/items/en-parent_child.txt";

sub loadParents() {
   my $child;
   my $parent;
   my $webParent;

   open(PARENT, "<", "$parent_file") or die "cannot open < $parent_file: $!";
   while(<PARENT>) {
      if(/^child_material_no\|/) {
         $child = $_;
         $child =~ s/[\r\n]+$//;
         $child =~ s/^child_material_no\|//;
      }
      elsif(/^parent_material_no\|/) {
         $parent = $_;
         $parent =~ s/[\r\n]+$//;
         $parent =~ s/^parent_material_no\|//;
         if($child ne $parent) {
            $parent{lc($child)} = lc($parent);
         }
      }
      elsif(/^web_parent_material_no\|/) {
         $webParent = $_;
         $webParent =~ s/[\r\n]+$//;
         $webParent =~ s/^web_parent_material_no\|//;
         if($child ne $webParent) {
            $webParent{lc($child)} = lc($webParent);
         }
      }
   }
}

sub loadCarts() {
   my $action;
   $i=0;
   open(CART, "<", "$cartFile") or die "cannot open < $cartFile: $!";
   while(<CART>) {
      $i++;
      if($i<6) {
         next;
      }
      @cols = split /\t/;
      $action = $cols[0];
      $product = lc($cols[2]);
      $cookie = $cols[7];
      $session = $cols[8];

      if($product ne '' && $cookie ne '' && $session ne '') {
         $carts{"${cookie}_${session}"}{$product} = $action;
         if(defined($parent{$product})) {
            $carts{"${cookie}_${session}"}{$parent{$product}} = $action;
         }
         if(defined($webParent{$product})) {
            $carts{"${cookie}_${session}"}{$webParent{$product}} = $action;
         }
      }
   }
   close(CART);
}

loadParents();
loadCarts();

$i=0;
while(<>) {
   $i++;
   if($i<6) {
      next;
   }

   @cols = split /\t/;
   $page = lc($cols[0]);
   $date = $cols[2]; # 2014-09-02 14:31:29
   ($year,$mon,$mday,$hour,$min,$sec) = split(/[\s\-:]+/, $date);
   $time = timelocal($sec,$min,$hour,$mday,$mon-1,$year);
   $cookie = $cols[6];
   $session = $cols[7];
   $product = lc($cols[9]);
   $url = lc($cols[13]);
   $keyword = "";

   $url =~ s/#.*//; # Avoid false matches in client-side url
   if(($url =~ /searchquery=[^&#]+/ || $url =~ /redirect=[^&#]+/ || $url =~ /ntt-.*?[\?\/]/) && $page =~ /\/search\// && $cookie ne '' && $session ne '') {
      # Search result page.  Order here is important.  For example, there could be a redirect to an L3 with a keyword refinement.  The redirect needs to take precedence over the Ntt.
      if($url =~ /searchquery=([^&#]+)/) {
         $keyword = $1;
      }
      elsif($url =~ /redirect=([^&#]+)/) {
         $keyword = $1;
      }
      elsif($url =~ /ntt-(.*?)[\?\/]/) {
         $keyword = $1;
      }
      else {
         $keyword = '';
      }

      $keyword =~ s/\|.*//;
      if($keyword =~ /^\d[^ ]+\d$/) {
         # Skip item number searches
         $keyword = '';
      }
      if($keyword =~ /img|http|=/) {
         $keyword = '';
      }
      if($keyword =~ /[^[:print:]]/) {
         $keyword = '';
      }
      $keyword =~ s/\|/ /g;
      $keyword =~ s/,/ /g;
      $keyword =~ s/\\/ /g;
      $keyword =~ s/\+/ /g;
      $keyword =~ s/[ ]+/ /g;
      $keyword =~ s/^[ ]//g;
      $keyword =~ s/[ ]$//g;

      $sessions{"${cookie}_${session}"}{$time} = "K:".$keyword;
   }
   elsif($page =~ /\/product\// && $cookie ne '' && $session ne '') {
      # Product page
      $url =~ s/^.*\/product\/quickview\///;
      $url =~ s/^.*\/product\/grainger\///;
      $url =~ s/^.*\/product\///;
      $url =~ s/\/.*//;
      $url =~ s/\?.*$//;
      $url =~ s/\&.*$//;
      $url =~ s/^.*\-//;

      if($product ne '' && $product ne '-') {
         $sessions{"${cookie}_${session}"}{$time} = "P:".$product;
      }
      elsif($url ne '') {
         $sessions{"${cookie}_${session}"}{$time} = "P:".$url;
      }
      else {
         $sessions{"${cookie}_${session}"}{$time} = "W";
      }
   }
   else {
      $sessions{"${cookie}_${session}"}{$time} = "W";
   }
}

foreach $customer (keys %sessions) {
   $eventKeyword = undef;
   $eventProduct = undef;
   foreach $time (sort {$a <=> $b} (keys %{$sessions{$customer}})) {
      $date=strftime '%Y-%m-%d %H:%M:%S', localtime $time;
      if($sessions{$customer}{$time} =~ /K:(.+)/) {
         # Search result page
         if(defined($1)) {
            $eventKeyword = $1;
         }
         else {
            $eventKeyword = undef;
         }
   
         $eventProduct = undef;
      }
      elsif($sessions{$customer}{$time} =~ /P:(.+)/) {
         # Product page
         if(defined($1)) {
            $eventProduct = $1;
         }
         else {
            $eventProduct = undef;
         } 
   
         # Check if the keyword is the product numbet search and ignore the event
         if(defined($eventProduct) && $eventProduct ne '' && defined($eventKeyword) && $eventKeyword ne '' && $eventKeyword ne $eventProduct) {
            # Event!
            #print "$customer $eventProduct $CART_PURCHASE \n";
            if(defined($carts{$customer}{$eventProduct}) && $carts{$customer}{$eventProduct} eq $CART_PURCHASE) {
               # PURCHASE event!
               print "$eventKeyword,$eventProduct,$date,$customer,0,0,1\n";
            }
            elsif(defined($carts{$customer}{$eventProduct}) && $carts{$customer}{$eventProduct} eq $CART_ADD) {
               # ATC event!
               print "$eventKeyword,$eventProduct,$date,$customer,0,1,0\n";
            }
            else {
               # VIEW event!
               print "$eventKeyword,$eventProduct,$date,$customer,1,0,0\n";
            }
         }
   
         $eventKeyword = undef;
         $eventProduct = undef;
      }
      else {
         $eventKeyword = undef;
         $eventProduct = undef;
      }
   }
}
   
