#!/bin/bash

dirPath=$(dirname "$0")
source "${dirPath}/vault-utils.sh"
source "${dirPath}/script-utils.sh"

bucket="gww-solr-qa-s3"
prefix="clickstream/data"   # include trailing slash if needed
date=$1
country=$2
pattern="data_*.csv.gz"
local_dir="/local/search/clickstream/data"


if [[ "$country" == "agi" ]]; then
    eventFile="agi_grainger"
    path="adobe_agi"
elif [[ "$country" == "mx" ]]; then
    eventFile="mx_grainger"
    path="adobe_mx"
elif [[ "$country" == "us" ]]; then
    eventFile="grainger"
    path="adobe"

fi

dest_dir="${local_dir}/${path}/${date}"

if [ ! -d "${local_dir}" ]; then
    mkdir -p "${local_dir}"
fi

if [ ! -d "${dest_dir}" ]; then
    mkdir -p "${dest_dir}"
fi

echo "s3://${bucket}/${prefix}/${country}/${date}/" "${dest_dir}"
aws s3 sync "s3://${bucket}/${prefix}/${country}/${date}/" "${dest_dir}"
pushd "${dest_dir}"
zcat *.csv.gz > ${dest_dir}/../clickstream-${date}.tsv
popd

