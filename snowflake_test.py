# python
import os
import sys
import pandas as pd
import argparse
import snowflake.connector

# Snowflake credentials (from env)
user = os.environ['SNOWSQL_USER']
password = os.environ['SNOWSQL_PWD']
account = os.environ['SNOWFLAKE_ACCOUNT']
destination_s3_path = os.environ['SNOW_DESTINATION_S3_PATH']

country_to_username = {
  "mx": "wwgraingergcommx",
  "us": "wwgraingerglobalprod",
  "agi": "wwgraingeracklands",
  # Add more mappings as needed
}
def execute_sql_get_data(cur, sql):
    cur.execute(sql)
    return pd.DataFrame.from_records(cur.fetchall(), columns=[i[0] for i in cur.description])

def execute_sql(cur, sql):
    cur.execute(sql)

def parse_args():
  parser = argparse.ArgumentParser(description="Snowflake data extraction script")
  parser.add_argument("--date", required=True, help="Date in YYYY-MM-DD format")
  parser.add_argument("--country", required=True, help="Country (e.g., wwgraingeracklands)")
  return parser.parse_args()

# Example usage:
# args = parse_args()
# print("Date:", args.date)
# print("Country:", args.country)
#
# # Example SQL query
# sql = f"SELECT * FROM PUBLISH.DEA.HITS_DAILY_EDV WHERE TO_DATE(date_time) = '{args.date}' AND username = '{args.country}' LIMIT 5"
#
# # Connect and fetch data
# snow_conn = snowflake.connector.connect(
#     user=user,
#     password=password,
#     account=account,
#     warehouse="SEARCH_ENGINE_WH_M",
#     database="SEARCH_ENGINE",
#     schema="PUBLIC"
# )
# cur = snow_conn.cursor()
# try:
#     df = execute_sql_get_data(cur, sql)
#     print(df)
# finally:
#     cur.close()
#     snow_conn.close()
#    
def main():
  args = parse_args()
  run_date = args.date
  country = args.country

  print("Date:", args.date)
  print("Country:", args.country)

  snow_conn = snowflake.connector.connect(
      user=user,
      password=password,
      account=account,
      warehouse="SEARCH_ENGINE_WH_M",
      database="SEARCH_ENGINE",
      schema="PUBLIC"
  )

  cur = snow_conn.cursor()
  print("Python version:", sys.version)
  print("Snowflake version:", snowflake.connector.__version__)

  sql_unload = f"""
  COPY INTO @{destination_s3_path}/{country}/{run_date}/
  FROM (
    select
      event_list,
      post_visid_high,
      post_visid_low,
      product_list,
      page_url,
      post_referrer,
      pagename,
      evar62,
      to_char(TO_TIMESTAMP_NTZ(date_time), 'YYYY-MM-DD HH24:MI:SS') as date_time,
      coalesce(post_evar19, '') as post_evar19,
      coalesce(post_evar20, '') as post_evar20,
      coalesce(post_evar11, '') as post_evar11
    from PUBLISH.DEA.HITS_DAILY_EDV
    where
      TO_TIMESTAMP_NTZ(date_time) >= TO_TIMESTAMP_NTZ('2025-03-01 00:00:00')
      and TO_TIMESTAMP_NTZ(date_time) < TO_TIMESTAMP_NTZ('2025-03-02 00:00:00')
      and username in ('wwgraingeracklands')
    order by date_time asc
  )
  FILE_FORMAT = (format_name = 'SEARCH_ENGINE.FILE_FORMAT.SEARCH_CSV')
  OVERWRITE = TRUE;
  """
  print(sql_unload)
  try:
      df = execute_sql_get_data(cur, sql_unload)
      # handle df if needed, e.g. print(df.head())
      print(df.head())
  finally:
      cur.close()
      snow_conn.close()

if __name__ == "__main__":
    main()
