select 
event_list,
post_visid_high,
post_visid_low,
product_list,
page_url,
post_referrer,
pagename,
evar62, -- this is different. language
to_char(TO_TIMESTAMP_NTZ(date_time), 'YYYY-MM-DD HH24:MI:SS') as date_time,
coalesce(post_evar19, '') as post_evar19,
coalesce(post_evar20, '') as post_evar20,
coalesce(post_evar11, '') as post_evar11
from publish.dea.hits_daily_edv --analytics.adobe_clickstream.hits_daily_v
where 
TO_TIMESTAMP_NTZ(date_time) >= TO_TIMESTAMP_NTZ('__start_date__ 00:00:00')
and TO_TIMESTAMP_NTZ(date_time) < TO_TIMESTAMP_NTZ('__end_date__ 00:00:00')
and username in ('wwgraingergcommx')
order by date_time asc;
