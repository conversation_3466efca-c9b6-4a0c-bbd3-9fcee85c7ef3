#!/usr/bin/perl

use strict;
use URI::Escape;

# Your sample record - manually corrected with proper field alignment
my $record = '20236=183,211,243,20208,100,101,102,103,106,108,109,113,114,127,133,134,147,148,154,155,156,157,158,159,160,161,162,163,164,166,167,170,178,188,191,192,193,194,10001,10002,10003,10004,10011,10015,10018,10020,10027,10031,10038,10053,10064,10065,10068,10074,10075,10076,10103,10125,10141,10149	4321280761088827931	1744885501547481282	None	https://www.grainger.com/category/tools/hand-tools/pliers/retaining-ring-pliers-sets?filters=webParentSkuKey&searchQuery=snap+ring+pliers&webParentSkuKey=WP13493127%7CWP13493128&sst=4&tv_optin=true	https://www.grainger.com/category/tools/hand-tools/files/material-removing-american-pattern-files?categoryIndex=1	grainger/category/tools/hand tools/pliers/retaining-ring plier	2025-02-01 00:00:00			::empty::	80	google.com';

# Expected headers based on the US headers file
my @header_names = qw(event_list post_visid_high post_visid_low product_list page_url post_referrer pagename date_time post_evar19 post_evar20 post_evar11 visit_keywords visit_page_num visit_ref_domain);

my %headers;
my $i = 0;
foreach my $k (@header_names) {
   $headers{$k} = $i++;
}
my $numFields = scalar(keys %headers);

print "Expected number of fields: $numFields\n";
print "Headers mapping:\n";
foreach my $k (sort keys %headers) {
    print "  $k => $headers{$k}\n";
}
print "\n";

# Parse the record
chomp($record);
my @tmpArr = split(/\t/, $record, -1);
print "Actual number of fields: " . scalar(@tmpArr) . "\n";
print "Field breakdown:\n";
for my $j (0..$#tmpArr) {
    my $field_name = $header_names[$j] || "UNKNOWN_FIELD_$j";
    print "  [$j] $field_name: '$tmpArr[$j]'\n";
}
print "\n";

# Check field count match
if(scalar(@tmpArr) != $numFields) {
    print "ERROR: Field count mismatch! Expected $numFields, got " . scalar(@tmpArr) . "\n";
    exit(1);
}

# Extract key fields for debugging - STRIP QUOTES
my $url = lc($tmpArr[$headers{'page_url'}]);
my $referrer = lc($tmpArr[$headers{'post_referrer'}]);
my $pageName = lc($tmpArr[$headers{'pagename'}]);
my $productList = lc($tmpArr[$headers{'product_list'}]);

# Strip quotes from field values
$url =~ s/^"//; $url =~ s/"$//;
$referrer =~ s/^"//; $referrer =~ s/"$//;
$pageName =~ s/^"//; $pageName =~ s/"$//;
$productList =~ s/^"//; $productList =~ s/"$//;

print "Key field values:\n";
print "  URL: '$url'\n";
print "  Referrer: '$referrer'\n";
print "  Page Name: '$pageName'\n";
print "  Product List: '$productList'\n";
print "\n";

# Check the main condition from mineEvents()
my $keyword = '';

# Keyword extraction function (from the script)
sub cleanKeyword($) {
   my $url = shift;

         # Grainger search result page.  Order here is important.  For example, there could be a redirect to an L3 with a keyword refinement.  The redirect needs to take precedence over the Ntt.
         if($url =~ /searchquery=([^&#]+)/) {
            $keyword = $1;
         }
         elsif($url =~ /okey=([^&#]+)/) {
            $keyword = $1;
         }
         elsif($url =~ /redirect=([^&#]+)/) {
            $keyword = $1;
         }
         elsif($url =~ /ntt-(.*?)[\?\/]/) {
            $keyword = $1;
         }
         else {
            $keyword = '';
         }

         $keyword = uri_unescape($keyword);
         $keyword =~ s/\|.*//;
         if($keyword =~ /^\d[^ ]+\d$/) {
            # Skip item number searches
            $keyword = '';
         }
         if($keyword =~ /img|http|https|=/) {
            $keyword = '';
         }
         if($keyword =~ /[^[:print:]]/) {
            $keyword = '';
         }
         $keyword =~ s/&quot;/"/g;
         if($keyword =~ /^"(.*)"$/) {
            $keyword = $1;
         }
         $keyword =~ s/\|/ /g;
         $keyword =~ s/,/ /g;
         $keyword =~ s/\\/ /g;
         $keyword =~ s/\+/ /g;
         $keyword =~ s/[ ]+/ /g;
         $keyword =~ s/^[ ]//g;
         $keyword =~ s/[ ]$//g;
    return $keyword;
}

# Check referrer for keywords
$referrer =~ s/#.*//;
print "Checking referrer: '$referrer'\n";
if(($referrer =~ /^http[s]?:\/\/www\.grainger\.com\/search/ || $referrer =~ /^http[s]?:\/\/www\.grainger\.com\/category/) && ($referrer =~ /searchquery=[^&#]+/ || $referrer =~ /redirect=[^&#]+/ || $referrer =~ /okey=[^&#]+/ || $referrer =~ /ntt-.*?[\?\/]/)) {
    print "Referrer matches search pattern - extracting keyword\n";
    $keyword = cleanKeyword($referrer);
    print "Extracted keyword from referrer: '$keyword'\n";
} else {
    print "Referrer does NOT match search pattern\n";
}

# Check URL for keywords
$url =~ s/#.*//;
print "Checking URL: '$url'\n";
if(($url =~ /^http[s]?:\/\/www\.grainger\.com\/search/ || $url =~ /^http[s]?:\/\/www\.grainger\.com\/category/) && ($url =~ /searchquery=[^&#]+/ || $url =~ /redirect=[^&#]+/ || $url =~ /okey=[^&#]+/ || $url =~ /ntt-.*?[\?\/]/)) {
    print "URL matches search pattern - extracting keyword\n";
    my $url_keyword = cleanKeyword($url);
    print "Extracted keyword from URL: '$url_keyword'\n";
    if ($keyword eq '') {
        $keyword = $url_keyword;
    }
} else {
    print "URL does NOT match search pattern\n";
}

print "Final keyword: '$keyword'\n";

# Check the main processing condition
print "\nMain condition checks:\n";
print "  URL contains /product/: " . ($url =~ /\/product\// ? "YES" : "NO") . "\n";
print "  Page name contains 'mini idp': " . ($pageName =~ /mini idp/ ? "YES" : "NO") . "\n";
print "  Page name contains 'add to cart' AND product list contains 'evar40=lves': " . (($pageName =~ /add to cart/ && $productList =~ /evar40=lves/) ? "YES" : "NO") . "\n";
print "  URL does NOT contain /compare/: " . ($url !~ /\/compare/ ? "YES" : "NO") . "\n";
print "  Keyword is not empty/special: " . ($keyword ne "::empty::" && $keyword ne "-" && $keyword ne "" && $keyword !~ /grainger/ ? "YES" : "NO") . "\n";

my $main_condition = (($url =~ /\/product\// || $pageName =~ /mini idp/ || ($pageName =~ /add to cart/ && $productList =~ /evar40=lves/))
        && $url !~ /\/compare/ && $keyword ne "::empty::" && $keyword ne "-" && $keyword ne "" && $keyword !~ /grainger/);

print "  OVERALL MAIN CONDITION: " . ($main_condition ? "PASS" : "FAIL") . "\n";

if (!$main_condition) {
    print "\nRecord will be SKIPPED because main condition failed.\n";
    print "Most likely reason: No keyword found (keyword is empty)\n";
} else {
    print "\nRecord would be PROCESSED\n";
}
