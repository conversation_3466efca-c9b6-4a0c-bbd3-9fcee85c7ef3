#!/usr/bin/perl -w

my $i=0;
my $product;
my $keyword;
my $referral;
my $date;
my $customer;
my $cookie;
my $session;

my @fields = qw(FIRST_VISIT_DATE ACQUISITION_SOURCE_URL DESTINATION_URL IP_ADDRESS SESSION_START_DATE REFERRAL_NAME REFERRAL_URL REFERRAL_TYPE SEARCH_WORD DESTINATION_URL COOKIE_ID SESSION_ID BROWSER_OS_NAME );

while(<>) {
   $i++;
   if($i<6) {
      next;
   }
   @cols = split /\t/;
   $product = lc($cols[9]);
   $keyword = lc($cols[8]);

   if($product =~ /www\.grainger\.com\/product/ && $keyword ne "-" && $keyword ne "") {
      $product =~ s/^.*\/product\/quickview\///;
      $product =~ s/^.*\/product\/grainger\///;
      $product =~ s/^.*\/product\///;
      $product =~ s/\/.*//;
      $product =~ s/\?.*$//;
      $product =~ s/\&.*$//;
      $product =~ s/^.*\-//;
      
      $referral = lc($cols[5]);
   
      if(!defined($referral)) {
      	$referral = "Unknown";
      }
      
      $date = lc($cols[4]);
      $cookie = lc($cols[10]);
      $session = lc($cols[11]);
      $customer = "${cookie}_${session}";

      # If the keyword is a product search then ignore the event      
      if ($keyword ne $product) {
      	print "$keyword,$product,$date,$customer,1,0,0,$referral\n";
      }
   }
}

