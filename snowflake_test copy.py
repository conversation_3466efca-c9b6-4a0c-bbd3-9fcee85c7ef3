# python
import os
import sys
import pandas as pd
import snowflake.connector

# Snowflake credentials (from env)
user = os.environ['SNOWSQL_USER']
password = os.environ['SNOWSQL_PWD']
account = 'wwgrainger.us-east-1'

def execute_sql_get_data(cur, sql):
    cur.execute(sql)
    return pd.DataFrame.from_records(cur.fetchall(), columns=[i[0] for i in cur.description])

def execute_sql(cur, sql):
    cur.execute(sql)

def main():
    snow_conn = snowflake.connector.connect(
        user=user,
        password=password,
        account=account,
        warehouse="SEARCH_ENGINE_WH_M",
        database="SEARCH_ENGINE",
        schema="PUBLIC"
    )
    cur = snow_conn.cursor()
    print("Python version:", sys.version)
    print("Snowflake version:", snowflake.connector.__version__)

    sql_unload = f"""
    COPY INTO @SEARCH_ENGINE.STAGES.CLICKSTREAM_HITS_DAILY_QA
    FROM (
      select
        event_list,
        post_visid_high,
        post_visid_low,
        product_list,
        page_url,
        post_referrer,
        pagename,
        evar62,
        to_char(TO_TIMESTAMP_NTZ(date_time), 'YYYY-MM-DD HH24:MI:SS') as date_time,
        coalesce(post_evar19, '') as post_evar19,
        coalesce(post_evar20, '') as post_evar20,
        coalesce(post_evar11, '') as post_evar11
      from PUBLISH.DEA.HITS_DAILY_EDV
      where
        TO_TIMESTAMP_NTZ(date_time) >= TO_TIMESTAMP_NTZ('2025-03-01 00:00:00')
        and TO_TIMESTAMP_NTZ(date_time) < TO_TIMESTAMP_NTZ('2025-03-02 00:00:00')
        and username in ('wwgraingeracklands')
      order by date_time asc
    )
    FILE_FORMAT = (format_name = 'SEARCH_ENGINE.FILE_FORMAT.SEARCH_CSV')
    OVERWRITE = TRUE;
    """

    print(sql_unload)
    try:
        df = execute_sql_get_data(cur, sql_unload)
        # handle df if needed, e.g. print(df.head())
    finally:
        cur.close()
        snow_conn.close()

if __name__ == "__main__":
    main()
