# python
from datetime import datetime, timedelta
import os
import pandas as pd
import sys
import boto3
import argparse
import snowflake.connector

def execute_sql_get_data(cur, sql):
    """Execute a SQL statement and return result as a DataFrame."""
    cur.execute(sql)
    return pd.DataFrame.from_records(cur.fetchall(), columns=[i[0] for i in cur.description])

def execute_sql(cur, sql):
    """Execute a SQL statement without returning data."""
    cur.execute(sql)

def download_all_from_s3(bucket_name, prefix='', local_dir='.', aws_access_key_id=None, aws_secret_access_key=None, aws_session_token=None, region_name=None):
    """
    Download all files from an S3 bucket (optionally under a prefix) to a local directory.
    """
    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        aws_session_token=aws_session_token,
        region_name=region_name
    )
    s3 = session.client('s3')
    paginator = s3.get_paginator('list_objects_v2')
    for page in paginator.paginate(Bucket=bucket_name, Prefix=prefix):
        for obj in page.get('Contents', []):
            s3_key = obj['Key']
            rel_path = s3_key[len(prefix):] if prefix and s3_key.startswith(prefix) else s3_key
            local_path = os.path.join(local_dir, rel_path.lstrip('/'))
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            s3.download_file(bucket_name, s3_key, local_path)

def main():
    # Snowflake credentials from environment
    user = os.environ.get('SNOWSQL_USER')
    password = os.environ.get('SNOWSQL_PWD')
    account = os.environ.get('SNOWFLAKE_ACCOUNT')
    destination_s3_path = os.environ.get('SNOW_DESTINATION_S3_PATH')

    print("Python version:", sys.version)
    print("Snowflake connector version:", snowflake.connector.__version__)

    parser = argparse.ArgumentParser(description="Process date (MMDDYYYY) and country parameters.")
    parser.add_argument('--date', type=str, help='Date in MMDDYYYY format', required=True)
    parser.add_argument('--country', type=str, help='Country code', required=True)
    args = parser.parse_args()

    dt_raw = args.date
    country = args.country

    # Validate and parse date
    try:
        start_dt = datetime.strptime(dt_raw, "%m%d%Y")
    except ValueError:
        raise SystemExit("Invalid date format. Use MMDDYYYY (e.g. ********).")

    start_ts = start_dt.strftime("%Y-%m-%d 00:00:00")
    end_ts = (start_dt + timedelta(days=1)).strftime("%Y-%m-%d 00:00:00")

    stage_path = f"@SEARCH_ENGINE.STAGES.CLICKSTREAM_HITS_DAILY_QA"
    print(f"Processing date: {dt_raw} country: {country}")
    print(f"Stage path: {stage_path}")

    sql_unload = f"""
    COPY INTO {stage_path}
    FROM (
        SELECT
            event_list,
            post_visid_high,
            post_visid_low,
            product_list,
            page_url,
            post_referrer,
            pagename,
            evar62,
            to_char(TO_TIMESTAMP_NTZ(date_time), 'YYYY-MM-DD HH24:MI:SS') AS date_time,
            coalesce(post_evar19, '') AS post_evar19,
            coalesce(post_evar20, '') AS post_evar20,
            coalesce(post_evar11, '') AS post_evar11
        FROM PUBLISH.DEA.HITS_DAILY_EDV
        WHERE
            TO_TIMESTAMP_NTZ(date_time) >= TO_TIMESTAMP_NTZ('{start_ts}')
            AND TO_TIMESTAMP_NTZ(date_time) < TO_TIMESTAMP_NTZ('{end_ts}')
            AND username IN ('wwgraingeracklands')
        ORDER BY date_time ASC
    )
    FILE_FORMAT = (format_name = 'SEARCH_ENGINE.FILE_FORMAT.SEARCH_CSV')
    OVERWRITE = TRUE;
    """

    # Open connection, execute, and close cleanly
    conn = snowflake.connector.connect(
        user=user,
        password=password,
        account=account,
        warehouse="SEARCH_ENGINE_WH_M",
        database="SEARCH_ENGINE",
        schema="PUBLIC"
    )
    cur = conn.cursor()
    try:
        results_df = execute_sql_get_data(cur, sql_unload)
        # optionally inspect results
        print("COPY INTO returned rows:", len(results_df))
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    main()
