#!/usr/bin/env bash

set -e
set -o pipefail

#TS-42: Interaction Batch Feed
#TS-41: Interaction Mining job
#Because Dev, QA and Prod may run either BSD version or CoreUtils version, we need to feed in appropriate Date command directives
#Below steps identify whether it is BSD or coreutils
#Systems that run coreutils works with date -v directive
#Systems that run bsd version works with date -d directive


#Setup current directory and destination directory

wDir="`dirname \"$0\"`"
echo $wDir
cd $wDir
dDir="../../data/interactions"
dDir="../../click/interactions"
dDir="/local/search/clickstream/data"



useAnalytics=1

countryCode=$1

# If no countryCode is passed, default to U.S
scriptFile="getGrainger.pl"
eventFile="grainger"
path="adobe"

###########################################################
# If analytics is turned on use file from analytics
if [[ $useAnalytics -eq 1 ]]; then
    log=$wDir/logs/fetchGrainger.`date +%F`.log
    (
        if [[ "$countryCode" == "agi" ]]; then
            scriptFile="getGraingerIntl.pl"
            eventFile="agi_grainger"
            path="adobe_agi"
        elif [[ "$countryCode" == "mx" ]]; then
            scriptFile="getGraingerIntl.pl"
            eventFile="mx_grainger"
            path="adobe_mx"
        fi

        today=`date "+%Y-%m-%d"`
        today=$2

        echo "Starting $scriptFile script for $today $eventFile"
        parseScript=`./$scriptFile $dDir/$path/clickstream-${today}.tsv > $dDir/${eventFile}_${today}.event`

        echo "$scriptFile script completed"

        if [[ $parseScript -eq 0 ]]; then
            echo "Completed $scriptFile..."
        else
            echo "Errors during $scriptFile execution.  Please review logs."
        fi

        eventoutputfile=`stat -c %s $dDir/${eventFile}_${today}.event`
	echo "Event output file : $dDir/${eventFile}_${today}.event"
        echo "Event output file size : $eventoutputfile"

        env="${GCOM_ENV}"

        if [[ $eventoutputfile -eq 0 ]] ; then
            echo "The program /local/search/solrBuilders/solrInteractionBuilder/fetchGrainger.sh on [$env] produced zero records most likely due to no Adobe file $dDir/$path/clickstream.tsv being delivered. This is not critical, but needs to be addressed within a couple days to avoid rolling off significant data over time." | mailx -s "WARNING: Zero-byte output for customer interactions on [$env]" <EMAIL>
            echo "$dDir/${eventFile}_${today}.event is a zero byte file. Please review logs."
            exit 1
        fi
        echo "Fetch grainger completed successfully. Event file was created for $today"
    ) | tee $log
###########################################################
# Else call the regular script to pull the data from splunk
else
    parseScript=`./fetchGrainger_Splunk.sh`
fi 
